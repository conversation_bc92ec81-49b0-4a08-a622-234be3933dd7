const app = getApp();
import {hasPrivile} from '../../../utils/route';

// 常量配置
const CONSTANTS = {
  DEFAULT_HEIGHT: 600,
  MIN_HEIGHT: 400,
  HEIGHT_CALC_DELAY: 100
};

// 防抖函数
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func.apply(this, args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

Page({
  data: {
    // 图表列表数据
    chartList: [],
    listScrollHeight: CONSTANTS.DEFAULT_HEIGHT,

    // 图表入口数据（直接搬运pages/report/report的tbData）
    tbData: [
      {
        name: '产业概述',
        code: 'cygs'
      },
      {
        name: '市场规模',
        code: 'scgm'
      },
      {
        name: '竞争格局',
        code: 'jjjl'
      },
      {
        name: '产业链图谱',
        code: 'cyltp'
      },
      {
        name: '政策法规',
        code: 'zcfg'
      },
      {
        name: '技术创新',
        code: 'jscx'
      },
      {
        name: '行业数据',
        code: 'hysj'
      },
      {
        name: '企业分析',
        code: 'qyfx'
      }
    ],

    // 用户状态
    isLogin: false,
    isVip: false
  },

  onLoad(options) {
    this.initData();
  },

  onReady() {
    this.calculateHeight();
  },

  onShow() {
    this.checkUserStatus();
  },

  /**
   * 初始化数据
   */
  initData() {
    this.checkUserStatus();
    this.loadChartData();
  },

  /**
   * 检查用户状态
   */
  async checkUserStatus() {
    const isLogin = app.isLogin();
    const oldIsVip = this.data.isVip;
    let isVip = false;

    // 如果已登录，检查VIP状态
    if (isLogin) {
      try {
        const vipStatus = await hasPrivile({packageType: true});
        isVip = vipStatus !== '游客' && vipStatus !== '普通VIP';
      } catch (error) {
        console.error('检查VIP状态失败:', error);
        isVip = false;
      }
    }

    this.setData({isLogin, isVip}, () => {
      // 如果VIP状态发生变化，重新计算高度
      if (oldIsVip !== isVip) {
        this.calculateHeight();
      }
    });
  },

  /**
   * 加载图表数据（模拟数据，使用占位图）
   */
  loadChartData() {
    // 模拟图表数据，使用占位图替换
    const mockChartList = [
      {
        id: 1,
        title: '2024年市场趋势分析',
        imgUrl: 'https://picsum.photos/342/200?random=1',
        type: 'line',
        summary: '详细分析2024年市场发展趋势',
        publishTime: '2024-01-15',
        footerText: '东吴证券'
      },
      {
        id: 2,
        title: '行业对比数据分析',
        imgUrl: 'https://picsum.photos/342/200?random=2',
        type: 'bar',
        summary: '多维度行业数据对比分析',
        publishTime: '2024-01-14',
        footerText: '12346'
      },
      {
        id: 3,
        title: '市场份额分布图表',
        imgUrl: 'https://picsum.photos/342/200?random=3',
        type: 'pie',
        summary: '各行业市场份额占比分析',
        publishTime: '2024-01-13',
        footerText: '华泰证券'
      },
      {
        id: 4,
        title: '增长率散点分析',
        imgUrl: 'https://picsum.photos/342/200?random=4',
        type: 'scatter',
        summary: '企业增长率相关性分析',
        publishTime: '2024-01-12',
        footerText: '中信证券'
      },
      {
        id: 5,
        title: '销售额变化趋势',
        imgUrl: 'https://picsum.photos/342/200?random=5',
        type: 'line',
        summary: '年度销售额变化趋势分析',
        publishTime: '2024-01-11',
        footerText: '招商证券'
      },
      {
        id: 6,
        title: '用户增长对比报告',
        imgUrl: 'https://picsum.photos/342/200?random=6',
        type: 'bar',
        summary: '用户增长数据对比分析',
        publishTime: '2024-01-10',
        footerText: '国泰君安'
      },
      {
        id: 7,
        title: '区块链技术应用前景分析',
        imgUrl: 'https://picsum.photos/342/200?random=7',
        type: 'line',
        summary: '区块链技术在各行业的应用前景',
        publishTime: '2024-01-09',
        footerText: '海通证券'
      },
      {
        id: 8,
        title: '新零售模式创新研究',
        imgUrl: 'https://picsum.photos/342/200?random=8',
        type: 'pie',
        summary: '新零售模式的创新点和发展趋势',
        publishTime: '2024-01-08',
        footerText: '申万宏源'
      },
      {
        id: 9,
        title: '智能制造产业链分析',
        imgUrl: 'https://picsum.photos/342/200?random=9',
        type: 'scatter',
        summary: '智能制造产业链的投资机会',
        publishTime: '2024-01-07',
        footerText: '兴业证券'
      },
      {
        id: 10,
        title: '新能源汽车供应链研究',
        imgUrl: 'https://picsum.photos/342/200?random=10',
        type: 'bar',
        summary: '新能源汽车供应链的发展现状',
        publishTime: '2024-01-06',
        footerText: '广发证券'
      },
      {
        id: 11,
        title: '数字经济发展趋势报告',
        imgUrl: 'https://picsum.photos/342/200?random=11',
        type: 'line',
        summary: '数字经济的发展趋势和投资机会',
        publishTime: '2024-01-05',
        footerText: '长江证券'
      },
      {
        id: 12,
        title: '生物医药行业深度调研',
        imgUrl: 'https://picsum.photos/342/200?random=12',
        type: 'pie',
        summary: '生物医药行业的发展前景分析',
        publishTime: '2024-01-04',
        footerText: '安信证券'
      },
      {
        id: 13,
        title: '碳中和投资机会分析',
        imgUrl: 'https://picsum.photos/342/200?random=13',
        type: 'scatter',
        summary: '碳中和背景下的投资机会',
        publishTime: '2024-01-03',
        footerText: '国信证券'
      },
      {
        id: 14,
        title: '半导体产业链研究',
        imgUrl: 'https://picsum.photos/342/200?random=14',
        type: 'bar',
        summary: '半导体产业链的投资价值分析',
        publishTime: '2024-01-02',
        footerText: '方正证券'
      },
      {
        id: 15,
        title: '消费升级趋势分析',
        imgUrl: 'https://picsum.photos/342/200?random=15',
        type: 'line',
        summary: '消费升级带来的投资机会',
        publishTime: '2024-01-01',
        footerText: '光大证券'
      }
    ];

    this.setData({
      chartList: mockChartList
    });
  },

  /**
   * 计算列表高度（防抖处理）
   */
  calculateHeight: debounce(function () {
    try {
      wx.nextTick(() => {
        const query = wx.createSelectorQuery().in(this);

        // 获取各个容器的高度
        query.select('.header-text').boundingClientRect();
        query.select('.searchs').boundingClientRect();

        // 如果是VIP用户，还需要获取zt-container的高度
        if (this.data.isVip) {
          query.select('.zt-container').boundingClientRect();
        }

        query.exec(res => {
          const {windowHeight} = wx.getSystemInfoSync();

          // 动态获取实际高度
          const headerHeight = res[0]?.height || 0;
          const searchHeight = res[1]?.height || 0;

          let usedHeight;
          if (this.data.isVip) {
            // VIP用户：页面高度 - zt-grid高度 - top高度
            const ztHeight = res[2]?.height || 0;
            const ztTop = res[2]?.top || 0;
            usedHeight = ztTop + ztHeight + 20; // 20为安全边距
          } else {
            // 非VIP用户：页面高度 - search_container高度 - top高度
            const searchTop = res[1]?.top || 0;
            usedHeight = searchTop + searchHeight + 20; // 20为安全边距
          }

          // 计算可用高度
          const availableHeight = windowHeight - usedHeight;
          this.setData({
            listScrollHeight: Math.max(availableHeight, CONSTANTS.MIN_HEIGHT)
          });
        });
      });
    } catch (error) {
      // 使用默认高度
      this.setData({
        listScrollHeight: CONSTANTS.DEFAULT_HEIGHT
      });
    }
  }, CONSTANTS.HEIGHT_CALC_DELAY),

  /**
   * 搜索框点击事件
   */
  onSearchTap() {
    // 跳转到ResearchThreeList页面，并默认选择图表tab
    wx.navigateTo({
      url: '/childSubpackage/pages/ResearchThreeList/index?type=chart&from=reportTuBiao'
    });
  },

  /**
   * 图表入口点击事件（直接搬运pages/report/report的onTbClick）
   */
  onTbClick(e) {
    const {item} = e.currentTarget.dataset;
    // const url = `/childSubpackage/pages/tbReport/index?name=${item.name}&code=${item.code}`;
    // app.route(this, url);

    // 临时提示，后续可以取消注释上面的跳转逻辑
    wx.showModal({
      title: '图表分析',
      content: `即将查看${item.name}相关内容`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 图表点击事件
   */
  onChartClick(e) {
    const {item} = e.detail;

    if (!this.data.isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }

    wx.showModal({
      title: '查看图表',
      content: `即将查看图表：${item.title}`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * VIP支付成功回调
   */
  onVipPaySuccess() {
    // 重新检查用户状态
    this.checkUserStatus();

    wx.showToast({
      title: 'VIP开通成功',
      icon: 'success',
      duration: 2000
    });
  }
});
