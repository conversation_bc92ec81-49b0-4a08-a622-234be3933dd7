/* 登录缺省页面 */
.card-login {
  position: relative;
  display: flex;
  flex-direction: column;
  /* justify-content: center; */
  align-items: center;
  width: 100%;
  height: 100vh;
  background: #f7f7f7;
}

.card-login image {
  width: 230rpx;
  height: 230rpx;
  margin-top: 204rpx;
}

.card-login .txt {
  padding-top: 40rpx;
  padding-bottom: 48rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798C;
  line-height: 33rpx;
}

.business {
  background-color: #F7F7F7;
}

.gradient {
  background: -webkit-linear-gradient(top, #D2170D, #FFFFFF);
}

.board-title {
  height: 200rpx;
  display: flex;
  padding: 20rpx 30rpx;
  position: relative;
}

.board-title-name {
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  line-height: 38rpx;
}

.board-title-select {
  position: absolute;
  top: 22rpx;
  right: 44rpx;
  display: flex;
}

.select-items {
  margin-left: 30rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 33rpx;
  display: flex;
}

.select-icon {
  margin-left: 10rpx;
  width: 20rpx;
  height: 20rpx;
}

.mask-hide {
  background-color: transparent !important;
}

.board-content {
  position: relative;
  z-index: 999;
  margin-top: -150rpx;
  padding: 30rpx 20rpx;
  padding-bottom: 10rpx;
  display: flex;
  flex-wrap: wrap;
}

.team {
  justify-content: space-between;
}

.board-item {
  background-color: #fff;
  width: 325rpx;
  display: flex;
  margin-bottom: 20rpx;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  margin-left: 20rpx;
}

.board-item-left {
  height: 120rpx;
  width: 120rpx;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
}

.board-item-icon {
  margin: 24rpx 30rpx;
  height: 72rpx;
  width: 72rpx;
}

.board-item-right {
  margin-left: 30rpx;
}

.board-item-name {
  margin-top: 24rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 33rpx;
}

.board-item-number {
  margin-top: 10rpx;
  font-size: 28rpx;
  font-family: OPPOSans-Medium, OPPOSans;
  font-weight: 500;
  color: #74798C;
  line-height: 33rpx;
}

.grid-content {
  display: flex;
  justify-content: center;
}

.funnelChart {
  margin-top: 4rpx;
}

.indicator-show {
  height: 600rpx;
  width: 1000rpx;
  background-color: #fff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  padding-top: 48rpx;
}


.select-item-box {
  max-height: 480rpx;
  overflow-y: auto;
  padding: 0 24rpx;
}

.select-item {
  height: 96rpx;
  color: #74798C;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.select-item.active .select {
  width: 32rpx;
  height: 32rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAATlJREFUWEftlj1uwjAYhl8DGwi8komiMLGyU4aeoBfgBqh7pa6MHIKBWyBuwAGQMAONOlSy1KVDEqMgWQpJIP6hSSWS2dHz+P38+TNByR8pmY9KoErg8RLwXDru7vhGdl+hCXz26YzUsBDAwtnxt0iiMAEJP+9cgDV/g1H7+PNdiEASjgCTLuOskARuwf9cIA+eKbDvUfrEOLedESrwlIDn0g8ITEWIZ2fPD6YSqvALAc9tDYH6FiCN6JSaSujAUwl8DTqvocDKVEIXnnkGTCVM4Fe7QFfCFH6zDVUlbOC590CehC08VyBacE3iHnAlgSwJAiwFwbscLPG7XffuUB5GF0lIigCzgSsnIHnJctjCtQVi5ZjDJy9ypOrGHl+vXIL4T2ugMQF8G3ApT7IsYaME7rHzKoF/k8AJqsP2IbiZ1dsAAAAASUVORK5CYII=') no-repeat center center;
  background-size: 100% 100%;
}

.select-item.active {
  font-weight: 600;
  color: #E72410;
}

.select-item:not(:first-child)::before {
  content: " ";
  height: 2rpx;
  width: 100%;
  background: #EEEEEE;
  position: absolute;
  top: 0;
  transform: scaleY(0.5);
}

.left-triangle {
  width: 0;
  height: 0;
  position: absolute;
  bottom: 0;
  left: 0rpx;

}

.right-triangle {
  width: 0;
  height: 0;
  position: absolute;
  bottom: 0;
  right: 0rpx;

}

.funnelChart {
  position: relative;
}

.board-content-text {
  width: 100%;
  position: absolute;
  
  top: 20rpx;
}

.board-content-text view {
  text-align: center;
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 33rpx;
  margin-top: 2rpx;
}
