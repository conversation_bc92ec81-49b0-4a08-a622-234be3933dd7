<import src="/template/null/null"></import>
<view class="pages" bindtap="onPageTap">
  <!-- input -->
  <view class="searchs">
    <view class="s-input">
      <view class="s-input-img">
        <image
          src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png"
          mode="aspectFit"
        ></image>
      </view>
      <view class="s-input-item">
        <input
          class="s-input-item-i"
          type="text"
          placeholder="请输入关键字"
          placeholder-class="placeholder"
          bindfocus="onFocus"
          bindblur="onBlur"
          value="{{report_name}}"
          focus="{{inputShowed}}"
          bindinput="onInput"
          bindconfirm="onConfirm"
          confirm-type="search"
        />
        <view
          hidden="{{report_name.length <= 0}}"
          catchtap="onClear"
          class="input-clear"
        >
          <view class="clearIcon"></view>
        </view>
        <!-- <view class="search-cancel" bindtap="goBack" bindtap="goBack"
          >取消</view
        > -->
      </view>
    </view>
  </view>
  <!-- 搜索记录 -->
  <view class="history_wrap" hidden="{{!inputFocused || report_name}}">
    <block wx:if="{{historyList.length>0}}">
      <view class="page__autofit search_a">
        <view class="his_title">
          <text class="his_title_l">搜索记录</text>
          <view class="his_title_icon" bindtap="handleIcon" data-index="a">
            <image
              src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/delet.png"
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <view class="his_content">
          <!-- 内容 -->
          <view class="text-box">
            <block wx:for="{{historyList}}" wx:key="index">
              <view
                class="his_content_item"
                bindtap="historyTap"
                data-item="{{item}}"
              >
                {{item}}
              </view>
            </block>
          </view>
        </view>
      </view>
    </block>

    <!-- AI图片 -->
    <image
      class="ai_img"
      src="/image/report/r_ai_enter.png"
      bindtap="handleClick"
      data-type="ai"
    />

    <!-- 最新报告 - 只在报告tab且无搜索内容时显示 -->
    <view
      class="page__autofit search_b"
      hidden="{{!(browsingHistory.length>0 && currentTab === 'report' && !report_name)}}"
    >
      <view class="his_titles">
        <text class="his_title_l">最新报告</text>
      </view>
      <view class="his_content1">
        <!-- 内容 -->
        <view
          class="his_content1_item1"
          wx:for="{{browsingHistory}}"
          wx:key="index"
          bindtap="goDetail"
          data-item="{{item}}"
        >
          <text class="his_content1_item-l">{{item.enterprise_name}}</text>
          <text class="his_content1_item-r">{{item.create_time}}</text>
        </view>
        <!-- 占位 -->
        <view class="his_content1_item"> </view>
        <view class="his_content1_item"> </view>
      </view>
    </view>
  </view>

  <!-- 搜索结果内容 -->
  <view class="search-content" hidden="{{inputFocused && !report_name}}">
    <!-- 内容区域 -->
    <view class="content-container search-tabs ">
      <!-- 左侧 TabBar -->
      <view class="left-tabbar">
        <view
          class="tab-item {{currentTab === 'report' ? 'active' : ''}}"
          bindtap="onTabChange"
          data-tab="report"
        >
          <text class="tab-text">报告</text>
        </view>
        <view
          class="tab-item {{currentTab === 'chart' ? 'active' : ''}}"
          bindtap="onTabChange"
          data-tab="chart"
        >
          <text class="tab-text">图表</text>
        </view>
        <view
          class="tab-item {{currentTab === 'library' ? 'active' : ''}}"
          bindtap="onTabChange"
          data-tab="library"
        >
          <text class="tab-text">撼地智库</text>
        </view>
      </view>

      <!-- 右侧图片占位 -->
      <view class="right-image"
        ><image src="/image/report/v_ww.png" mode="aspectFit" />
        <text>问一问</text>
      </view>
    </view>

    <!-- 搜索结果列表 -->
    <view class="search-results">
      <!-- 撼地智库特殊列表 -->
      <view class="library-list">
        <block wx:if="{{currentTab === 'library'}}">
          <!-- 结果统计 -->
          <view class="company_num">
            <view class="text_left">
              共找到
              <text
                class="color_num"
                >{{libraryTotal || libraryReportList.length || 0}}</text
              >
              个相关报告
            </view>
          </view>
          <!-- 使用撼地智库列表组件 -->
          <LibraryReportList
            requestParams="{{libraryRequestParams}}"
            containerHeight="{{listScrollHeight}}"
            autoCheckVip="{{true}}"
            showRemainingInfo="{{true}}"
            bind:datachange="onLibraryDataChange"
            bind:error="onLibraryError"
            bind:reportclick="onLibraryReportClick"
          >
          </LibraryReportList>
        </block>
        <!-- 报告 -->
        <block wx:elif="{{currentTab === 'report'}}" class="normal-list">
          <view class="report_detail">
            <view class="title">
              <view class="cyzt_l">
                <text>产业专题</text>
                <text>共找到</text>
                <text>{{ztData.length}}</text>
                <text>个相关专题</text>
              </view>
            </view>
            <scroll-view
              class="zt-container"
              scroll-x="true"
              show-scrollbar="false"
            >
              <view class="zt-grid">
                <view
                  wx:for="{{ztData}}"
                  wx:key="index"
                  class="zt-item"
                  bindtap="onZtClick"
                  data-item="{{item}}"
                >
                  <image
                    class="zt-image"
                    src="{{item.img}}"
                    mode="aspectFill"
                  />
                  <view class="zt-name">{{item.name}}</view>
                </view>
              </view>
            </scroll-view>
            <!-- VIP用户显示统计信息 -->
            <view wx:if="{{isVip}}" class="company_num_container">
              <view class="company_num">
                <view class="text_left">
                  <text class="one">报告</text>
                  共找到
                  <text
                    class="color_num"
                    >{{libraryTotal || libraryReportList.length || 0}}</text
                  >
                  个相关报告
                </view>
                <view class="sort-dropdown" catchtap="onSortDropdownTap">
                  <text
                    class="sort-text {{showSortDropdown ? 'active' : ''}}"
                    >{{currentSortText}}</text
                  >
                  <image
                    class="sort-arrow "
                    src="{{showSortDropdown ? '/image/report/r_sort_a.png':'/image/report/r_sort.png'}}"
                    mode="aspectFit"
                  />
                </view>
              </view>
              <!-- 排序下拉选项 -->
              <view
                wx:if="{{showSortDropdown}}"
                class="sort-options"
                catchtap="onSortOptionsContainerTap"
              >
                <view
                  wx:for="{{sortOptions}}"
                  wx:key="value"
                  class="sort-option {{currentSort === item.value ? 'active' : ''}}"
                  catchtap="onSortOptionTap"
                  data-value="{{item.value}}"
                  data-text="{{item.text}}"
                >
                  <text class="option-text">{{item.text}}</text>
                  <image
                    wx:if="{{currentSort === item.value}}"
                    class="check-icon"
                    src="/image/report/r_gou.png"
                    mode="aspectFit"
                  />
                </view>
              </view>
            </view>
            <!-- 列表 -->
            <LibraryReportList
              requestParams="{{libraryRequestParams}}"
              containerHeight="{{listScrollHeight}}"
              autoCheckVip="{{true}}"
              showRemainingInfo="{{true}}"
              isReportTab="{{true}}"
              bind:datachange="onLibraryDataChange"
              bind:error="onLibraryError"
              bind:reportclick="onLibraryReportClick"
            >
            </LibraryReportList>
          </view>
        </block>
        <!-- 图表 -->
        <block wx:else class="normal-list1">
          <view class="chart-tab-container">
            <!-- 图表统计信息 -->
            <view class="company_num_container">
              <view class="company_num">
                <view class="text_left">
                  <text class="one">图表</text>
                  共找到
                  <text class="color_num">{{chartList.length || 0}}</text>
                  个相关图表
                </view>
                <view class="sort-dropdown" catchtap="onChartSortDropdownTap">
                  <text
                    class="sort-text {{showChartSortDropdown ? 'active' : ''}}"
                    >{{currentChartSortText}}</text
                  >
                  <image
                    class="sort-arrow"
                    src="{{showChartSortDropdown ? '/image/report/r_xsja.png' : '/image/report/r_xsj.png'}}"
                    mode="aspectFit"
                  />
                </view>
              </view>
              <!-- 排序下拉选项 -->
              <view
                wx:if="{{showChartSortDropdown}}"
                class="sort-options"
                catchtap="onChartSortOptionsContainerTap"
              >
                <view
                  wx:for="{{chartSortOptions}}"
                  wx:key="value"
                  class="sort-option {{currentChartSort === item.value ? 'active' : ''}}"
                  catchtap="onChartSortOptionTap"
                  data-value="{{item.value}}"
                  data-text="{{item.text}}"
                >
                  <text class="option-text">{{item.text}}</text>
                  <image
                    wx:if="{{currentChartSort === item.value}}"
                    class="check-icon"
                    src="/image/report/r_gou.png"
                    mode="aspectFit"
                  />
                </view>
              </view>
            </view>
            <!-- 图表列表组件 -->
            <ChartGridList
              chartList="{{chartList}}"
              containerHeight="{{listScrollHeight}}"
              isVip="{{isVip}}"
              showVipPage="{{false}}"
              showRemainingInfo="{{false}}"
              totalCount="{{chartList.length}}"
              bind:chartclick="onChartClick"
            />
          </view>
        </block>
      </view>
    </view>
  </view>
</view>
