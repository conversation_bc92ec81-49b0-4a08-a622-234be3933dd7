// components/HalfScreenPop/HalfScreenPop.js
/**
 * 使用方法：
 * <half-screen-pop title="测试标题" position="bottom" id="half-screen-pop">
    <view slot="customhead">
      自定义头部内容
    </view>
    <view class="con" slot="customContent">
      自定义主体内容
    </view>
  * </half-screen-pop>
  * 页面内通过visible属性控制弹出层显示/隐藏
  */
const app = getApp();
let routeArr = [
  'pages/home/<USER>',
  'pages/enterprise/index',
  'pages/report/report',
  'pages/card/card',
  'pages/mine/mine'
];
Component({
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  externalClasses: ['content-class'], //没有按钮的时候 内容部分展示圆角

  /**
   * 组件的属性列表
   */
  properties: {
    // 标题名称
    title: {
      type: String,
      value: ''
    },

    // 是否显示关闭按钮
    showCloseBtn: {
      type: Boolean,
      value: false
    },
    // 是否改变下间距
    changePadding: {
      type: Boolean,
      value: false
    },

    // 弹出层位置(top/bottom)
    position: {
      type: String,
      value: 'top'
    },

    // 点击遮罩层是否关闭弹出层
    _maskClosable: {
      type: Boolean,
      value: true
    },

    // 是否显示底部按钮
    showFooter: {
      type: Boolean,
      value: true
    },

    // 底部确定按钮自定义
    confirmBtnText: {
      type: String,
      value: '确定'
    },
    cancelBtnText: {
      type: String,
      value: '取消'
    },

    // 是否显示取消按钮
    showCancelBtn: {
      type: Boolean,
      value: true
    },

    // 开始距离，默认0(即从顶部/底部边缘触发,单位px/rpx)
    startDistance: {
      type: String,
      value: '0px'
    },

    // 是否禁用动画
    disableAnimation: {
      type: Boolean,
      value: false
    },

    // 控制组件显示/隐藏
    visible: {
      type: Boolean,
      value: false,
      observer(bl) {
        bl && this.showPop();
        // 这里注意一下，这种情况是外面一开始弹窗有，然后通过visible让其关闭，但是不会触发closePop
        !bl && this.closePop(false, 'biaoshi');
      }
    },
    showCancelCls: {
      //只有取消按钮的样式
      type: Boolean,
      value: false
    },
    footHeigh: {
      type: String
    },
    zIndex: {
      //有些地方需要提高层级
      type: Number
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    footHeighs: null, //底部按钮高度
    hide: true, // 控制弹出层显示/隐藏
    maskClass: 'hideCover', // 遮罩层显示/隐藏类(showCover/hideCover)
    height: 0, // 弹出层高度，动态获取，用于设置过渡动画
    screenWidth: 0 // 当前设备屏幕宽度
  },
  /**
   * 定义组件生命周期
   */
  lifetimes: {
    attached: function () {
      // 获取当前设备宽度，计算startDistance
      const screenWidth = wx.getSystemInfoSync().screenWidth;
      let {startDistance, footHeigh, position} = this.data;

      // 将rpx按比例转换成px
      startDistance =
        startDistance.indexOf('rpx') > 0
          ? (parseInt(startDistance) * screenWidth) / 750
          : parseInt(startDistance);
      // 计算底部按钮高度
      let num;
      let pages = getCurrentPages();
      if (pages.length > 0) {
        num = routeArr.includes(pages[0].route) ? '110rpx' : '168rpx';
      }
      let footHeighs = footHeigh
        ? footHeigh
        : position == 'bottom'
        ? app.globalData.isIphoneX
          ? num
          : '100rpx'
        : '110rpx';
      // console.log(footHeighs)

      this.setData({
        startDistance,
        footHeighs
      });
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 禁止遮罩层滚动穿透
    _disabledPenetrate() {
      return;
    },

    // 获取弹出层高度
    _getPopHeight(func) {
      const query = this.createSelectorQuery();
      query.select('.content-wrap').boundingClientRect();
      query.exec(res => {
        const height = res[0].height;
        this.setData({
          height
        });
        func.call(this, height);
      });
    },

    // 点击确定按钮
    submit() {
      this.triggerEvent('submit'); // 向外传递一个自定义事件
      this.closePop(false, 'biaoshi');
    },

    // 显示弹出层
    showPop() {
      let {height, disableAnimation} = this.data;
      this.setData({
        hide: false,
        maskClass: disableAnimation ? 'default' : 'showCover'
      });
      if (disableAnimation) return;
      height && this._setAnimate(height); // 设置弹出层过渡动画
      !height && this._getPopHeight(this._setAnimate); // 获取弹出层高度
    },

    // 关闭弹出层
    closePop(e, type = false) {
      let {hide, disableAnimation} = this.data;
      if (hide) return;
      if (e) {
        // 如果是通过节点触发，判断点击节点是否是遮罩层
        let {id} = e.target,
          {_maskClosable} = this.data;
        if (id === 'mask' && !_maskClosable) return;
      }

      if (disableAnimation) {
        this.setData({
          hide: true,
          visible: false
        });
      } else {
        this.setData({
          maskClass: 'hideCover'
        });
        this._setAnimate(0); // 设置弹出层过渡动画
        setTimeout(() => {
          this.setData({
            hide: true,
            visible: false
          });
          this.clearAnimation('.content-wrap'); // 移除弹出层过渡动画
        }, 150);
      }
      type && type == 'biaoshi' ? null : this.triggerEvent('close');
    },

    // 设置弹出层过渡动画
    _setAnimate(height) {
      const {startDistance} = this.data;
      const position = this.data.position; // 获取弹出层位置(top/bottom)
      this.animate(
        '.content-wrap',
        [
          {
            [position]: height
              ? -(height - startDistance) + 'px'
              : startDistance + 'px'
          },
          {
            [position]: height
              ? startDistance + 'px'
              : -(this.data.height - startDistance) + 'px'
          }
        ],
        100
      );
    }
  }
});
