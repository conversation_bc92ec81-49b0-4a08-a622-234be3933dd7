import {getReportPageListApi, addBevHis} from '../../../service/industryApi';
import {getHeight} from '../../../utils/height';
import {hasPrivile} from '../../../utils/route';

const app = getApp();

// Mock 数据
const MOCK_DATA = {
  // 搜索历史关键词
  searchHistory: [
    '新能源汽车',
    '人工智能',
    '半导体',
    '生物医药',
    '新材料',
    '5G通信',
    '云计算',
    '区块链'
  ],
  // 浏览历史企业
  browsingHistory: [
    {
      enterprise_name:
        '比亚迪股份有限公司1啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊',
      enterprise_id: '1001',
      create_time: '01-15'
    },
    {
      enterprise_name: '宁德时代新能源科技股份有限公司',
      enterprise_id: '1002',
      create_time: '01-14'
    },
    {
      enterprise_name: '华为技术有限公司',
      enterprise_id: '1003',
      create_time: '01-13'
    },
    {
      enterprise_name: '腾讯科技（深圳）有限公司',
      enterprise_id: '1004',
      create_time: '01-12'
    },
    {
      enterprise_name: '阿里巴巴（中国）有限公司',
      enterprise_id: '1005',
      create_time: '01-11'
    },
    {
      enterprise_name: '小米科技有限责任公司',
      enterprise_id: '1006',
      create_time: '01-10'
    },
    {
      enterprise_name: '京东方科技集团股份有限公司',
      enterprise_id: '1007',
      create_time: '01-09'
    },
    {
      enterprise_name: '中芯国际集成电路制造有限公司',
      enterprise_id: '1008',
      create_time: '01-08'
    },
    {
      enterprise_name: '京东方科技集团股份有限公司',
      enterprise_id: '1007',
      create_time: '01-09'
    },
    {
      enterprise_name: '中芯国际集成电路制造有限公司',
      enterprise_id: '1008',
      create_time: '01-08'
    }
  ],
  // 搜索结果数据 - 按类型分类
  searchResults: {
    report: [
      {
        id: 'report_1',
        title: '2024年新能源汽车产业报告',
        description:
          '详细分析新能源汽车行业发展趋势，包含市场规模、技术发展等内容。',
        type: '产业报告'
      },
      {
        id: 'report_2',
        title: '人工智能技术发展白皮书',
        description: '全面解读AI技术在各行业的应用现状和未来发展方向。',
        type: '技术报告'
      },
      {
        id: 'report_3',
        title: '半导体行业深度调研',
        description: '深入分析半导体产业链上下游企业发展状况和投资机会。',
        type: '调研报告'
      }
    ],
    chart: [
      {
        id: 'chart_1',
        title: '新能源汽车销量趋势图',
        description: '展示近5年新能源汽车销量变化趋势和预测数据。',
        type: '趋势图表'
      },
      {
        id: 'chart_2',
        title: 'AI企业分布热力图',
        description: '全国AI企业地理分布情况和产业集群分析图表。',
        type: '分布图表'
      }
    ],
    library: [
      {
        id: 'library_1',
        title: '撼地智库：产业链分析',
        description: '基于大数据的产业链深度分析，提供专业的投资建议。',
        type: '智库分析'
      },
      {
        id: 'library_2',
        title: '撼地智库：市场洞察',
        description: '结合AI算法的市场趋势预测和行业发展洞察报告。',
        type: '市场分析'
      },
      {
        id: 'library_3',
        title: '撼地智库：企业画像',
        description: '多维度企业画像分析，助力精准招商和投资决策。',
        type: '企业分析'
      }
    ]
  }
};

Page({
  data: {
    // 搜索相关
    inputShowed: false, // 输入框是否聚焦
    inputFocused: false, // 输入框是否被聚焦过
    report_name: '', // 搜索关键词

    // 历史数据
    historyList: [...MOCK_DATA.searchHistory],
    browsingHistory: [...MOCK_DATA.browsingHistory],

    // 搜索结果相关
    currentTab: 'report', // 当前选中的tab
    searchResults: MOCK_DATA.searchResults,
    currentResults: [...MOCK_DATA.searchResults.report], // 当前显示的结果

    // 图表相关数据
    chartList: [], // 图表列表数据

    // 撼地智库相关数据
    libraryReportList: [], // 撼地智库研报列表，初始为空
    libraryTotal: 0, // 撼地智库研报总数
    libraryRequestParams: {
      type: 'chainMap', // 热门研报，与thinkTankList保持一致
      keyword: ' ' // 使用当前搜索关键词
    },
    RequestUrlFn: getReportPageListApi, // API 请求函数
    listScrollHeight: 600, // 列表滚动高度

    // 排序相关数据
    showSortDropdown: false, // 是否显示排序下拉框
    currentSort: 'default', // 当前排序方式
    currentSortText: '默认排序', // 当前排序文本
    sortOptions: [
      {value: 'default', text: '默认排序'},
      {value: 'time', text: '时间排序'},
      {value: 'hot', text: '热度排序'}
    ],

    // 图表排序相关数据
    showChartSortDropdown: false, // 是否显示图表排序下拉框
    currentChartSort: 'all', // 当前图表排序方式
    currentChartSortText: '全部图表', // 当前图表排序文本
    chartSortOptions: [
      {value: 'all', text: '全部图表'},
      {value: 'cygs', text: '产业概述'},
      {value: 'scgm', text: '市场规模'},
      {value: 'jjjl', text: '竞争格局'},
      {value: 'cyltp', text: '产业链图谱'},
      {value: 'zcfg', text: '政策法规'},
      {value: 'jscx', text: '技术创新'},
      {value: 'hysj', text: '行业数据'},
      {value: 'qyfx', text: '企业分析'}
    ],

    // 配置
    scrollHeight: 600,
    isLogin: app.isLogin(),
    isVip: false, // VIP状态

    // 高度计算相关
    isHeightCalculated: false, // 是否已计算过高度
    searchScrollHeight: 600, // 搜索结果区域高度
    tabsHeight: 0, // tabs区域高度
    ztData: [
      // 报告相关动态获取
      {
        name: '生物医药',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      },
      {
        name: '智能网联新能源汽车专题',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      },
      {
        name: '低空经济',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      },
      {
        name: '人形机器人',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      },
      {
        name: '智能网联新能源汽车专题',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      },
      {
        name: '生物医药',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      },
      {
        name: '生物医药',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      },
      {
        name: '生物医药',
        img: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png'
      }
    ]
  },

  onLoad(options) {
    console.log('页面加载参数:', options);
    this.initPage(options);
    this.loadChartData();
  },

  onShow() {
    this.updateLoginStatus();
    this.checkVipStatus();
    // 重新计算高度
    this.calculateAllHeights();
  },
  onReady() {
    console.log('页面渲染完成');
    // 页面渲染完成后计算高度
    this.calculateAllHeights();
  },

  /**
   * 初始化页面
   * @param {Object} options - 页面参数
   */
  initPage(options = {}) {
    // 检查来源页面和参数
    const {type, from} = options;

    // 如果从 ReportTuBiao 页面进来，且type为chart，默认选中图表tab
    if (from === 'reportTuBiao' && type === 'chart') {
      this.setData({
        currentTab: 'chart',
        currentResults: [...this.data.searchResults.chart]
      });
      console.log('从研报图表页面进入，默认选中图表tab');
    }
    // 如果从 thinkTankList 页面进来，且有输入内容，默认选中撼地智库
    else if (from === 'thinkTankList' || type === 'hdzk') {
      this.setData({
        currentTab: 'library',
        currentResults: [...this.data.searchResults.library]
      });
      console.log('从撼地智库页面进入，默认选中撼地智库tab');
    }
  },

  /**
   * 更新登录状态
   */
  updateLoginStatus() {
    const {login} = app.globalData;
    this.setData({
      isLogin: login
    });
  },

  /**
   * 检查VIP状态
   */
  async checkVipStatus() {
    if (!this.data.isLogin) {
      this.setData({isVip: false});
      return;
    }

    try {
      const vipStatus = await hasPrivile({packageType: true});
      const isVip = vipStatus !== '游客' && vipStatus !== '普通VIP';
      this.setData({isVip});
    } catch (error) {
      this.setData({isVip: false});
    }
  },

  /**
   * 加载图表数据（模拟数据，与ReportTuBiao页面保持一致）
   */
  loadChartData() {
    // 使用与ReportTuBiao页面相同的模拟图表数据
    const mockChartList = [
      {
        id: 1,
        title: '2024年市场趋势分析',
        imgUrl: 'https://picsum.photos/342/200?random=1',
        type: 'line',
        summary: '详细分析2024年市场发展趋势',
        publishTime: '2024-01-15',
        footerText: '东吴证券',
        category: 'cygs' // 产业概述
      },
      {
        id: 2,
        title: '行业对比数据分析',
        imgUrl: 'https://picsum.photos/342/200?random=2',
        type: 'bar',
        summary: '多维度行业数据对比分析',
        publishTime: '2024-01-14',
        footerText: '12346',
        category: 'hysj' // 行业数据
      },
      {
        id: 3,
        title: '市场份额分布图表',
        imgUrl: 'https://picsum.photos/342/200?random=3',
        type: 'pie',
        summary: '各行业市场份额占比分析',
        publishTime: '2024-01-13',
        footerText: '华泰证券',
        category: 'scgm' // 市场规模
      },
      {
        id: 4,
        title: '增长率散点分析',
        imgUrl: 'https://picsum.photos/342/200?random=4',
        type: 'scatter',
        summary: '企业增长率相关性分析',
        publishTime: '2024-01-12',
        footerText: '中信证券',
        category: 'jjjl' // 竞争格局
      },
      {
        id: 5,
        title: '销售额变化趋势',
        imgUrl: 'https://picsum.photos/342/200?random=5',
        type: 'line',
        summary: '年度销售额变化趋势分析',
        publishTime: '2024-01-11',
        footerText: '招商证券',
        category: 'cyltp' // 产业链图谱
      },
      {
        id: 6,
        title: '用户增长对比报告',
        imgUrl: 'https://picsum.photos/342/200?random=6',
        type: 'bar',
        summary: '用户增长数据对比分析',
        publishTime: '2024-01-10',
        footerText: '国泰君安',
        category: 'scgm' // 市场规模
      },
      {
        id: 7,
        title: '政策法规影响分析',
        imgUrl: 'https://picsum.photos/342/200?random=7',
        type: 'line',
        summary: '政策法规对行业发展的影响分析',
        publishTime: '2024-01-09',
        footerText: '海通证券',
        category: 'zcfg' // 政策法规
      },
      {
        id: 8,
        title: '技术创新趋势图表',
        imgUrl: 'https://picsum.photos/342/200?random=8',
        type: 'pie',
        summary: '新技术创新发展趋势分析',
        publishTime: '2024-01-08',
        footerText: '申万宏源',
        category: 'jscx' // 技术创新
      },
      {
        id: 9,
        title: '企业分析报告图表',
        imgUrl: 'https://picsum.photos/342/200?random=9',
        type: 'scatter',
        summary: '重点企业经营状况分析',
        publishTime: '2024-01-07',
        footerText: '兴业证券',
        category: 'qyfx' // 企业分析
      }
    ];

    this.setData({
      chartList: mockChartList
    });
  },

  /**
   * 清空搜索输入
   */
  onClear() {
    this.setData({
      report_name: ''
    });
  },

  /**
   * 输入框获得焦点
   */
  onFocus() {
    console.log('输入框获得焦点');
    this.setData({
      inputShowed: true,
      inputFocused: true
    });
  },

  /**
   * 输入框失去焦点
   */
  onBlur() {
    console.log('输入框失去焦点');
    this.setData({
      inputShowed: false,
      inputFocused: false // 失去焦点时重置聚焦状态
    });
  },

  /**
   * 输入框内容变化
   */
  onInput(e) {
    const keyword = e.detail.value;
    this.setData({
      report_name: keyword
    });

    // 更新LibraryReportList的搜索参数
    this.updateLibrarySearchParams(keyword);
  },

  /**
   * 确认搜索
   */
  onConfirm(e) {
    const keyword = e.detail.value.trim();
    if (keyword) {
      this.addToSearchHistory(keyword);
      this.updateLibrarySearchParams(keyword);
    }
  },

  /**
   * 更新LibraryReportList的搜索参数
   */
  updateLibrarySearchParams(keyword) {
    const newRequestParams = {
      ...this.data.libraryRequestParams,
      keyword: keyword || ''
    };
    this.setData({libraryRequestParams: newRequestParams});
  },

  /**
   * 添加到搜索历史
   * @param {string} keyword - 搜索关键词
   */
  addToSearchHistory(keyword) {
    const {historyList} = this.data;
    const newHistoryList = [...historyList];

    // 如果已存在，先移除
    const existIndex = newHistoryList.indexOf(keyword);
    if (existIndex !== -1) {
      if (existIndex === 0) return; // 已在第一位，无需操作
      newHistoryList.splice(existIndex, 1);
    }

    // 添加到第一位
    newHistoryList.unshift(keyword);

    // 限制历史记录数量
    if (newHistoryList.length > 10) {
      newHistoryList.pop();
    }

    this.setData({
      historyList: newHistoryList
    });
  },

  /**
   * 点击搜索历史
   */
  historyTap(e) {
    const keyword = e.target.dataset.item;
    this.setData({
      inputShowed: true,
      report_name: keyword
    });
    // 更新搜索参数
    this.updateLibrarySearchParams(keyword);
  },

  /**
   * 点击删除图标
   */
  handleIcon(e) {
    const type = e.currentTarget.dataset.index;

    switch (type) {
      case 'a':
        this.clearSearchHistory();
        break;
      default:
        break;
    }
  },

  /**
   * 清空搜索历史
   */
  clearSearchHistory() {
    wx.showModal({
      title: '删除搜索',
      content: '确定要删除最近搜索?',
      success: res => {
        if (res.confirm) {
          this.setData({
            historyList: []
          });
        }
      }
    });
  },

  /**
   * 处理AI图片点击事件
   */
  handleClick(e) {
    const {isLogin} = this.data;
    const type = e.currentTarget.dataset?.type;

    if (!isLogin) {
      this.redirectToLogin();
      return;
    }
    // 非vip点击 toast提示 不然就跳转到h5
    wx.showToast({
      title: 'AI功能开发中',
      icon: 'none'
    });
  },

  /**
   * 跳转到登录页面
   */
  redirectToLogin() {
    app.route(this, '/pages/login/login');
  },

  /**
   * 浏览历史点击事件
   * @param {Object} e - 事件对象
   */
  goDetail(e) {
    const item = e.currentTarget.dataset.item;
    console.log('点击浏览历史:', item);

    // TODO: 后续改造为新的逻辑
    // 可能的实现：
    // 1. 跳转到企业详情页
    // 2. 添加到搜索历史
    // 3. 其他业务逻辑
  },

  /**
   * Tab 切换事件
   * @param {Object} e - 事件对象
   */
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    const {searchResults} = this.data;

    this.setData({
      currentTab: tab,
      currentResults: [...searchResults[tab]]
    });

    // 根据不同tab重新计算高度
    setTimeout(() => {
      this.calculateHeightByTab(tab);
    }, 200);
  },

  /**
   * 根据tab类型计算高度
   */
  calculateHeightByTab(tab) {
    const heightCalculators = {
      library: () => this.calculateLibraryHeight(),
      report: () => this.calculateReportHeight(),
      chart: () => this.calculateChartHeight(),
      default: () => this.calculateGeneralHeight()
    };

    const calculator = heightCalculators[tab] || heightCalculators.default;
    calculator();
  },

  /**
   * 搜索结果点击事件
   * @param {Object} e - 事件对象
   */
  onResultClick(e) {
    const item = e.currentTarget.dataset.item;
    const {currentTab} = this.data;

    console.log('点击搜索结果:', item);

    // 根据不同tab处理不同逻辑
    switch (currentTab) {
      case 'report':
        this.handleReportClick(item);
        break;
      case 'chart':
        this.handleChartClick(item);
        break;
      case 'library':
        this.handleLibraryClick(item);
        break;
      default:
        console.log('未知tab类型:', currentTab);
        break;
    }
  },

  /**
   * 处理报告点击
   */
  handleReportClick(item) {
    wx.showToast({
      title: `点击了报告: ${item.title}`,
      icon: 'none'
    });
    // TODO: 跳转到报告详情页
  },

  /**
   * 处理图表点击
   */
  handleChartClick(item) {
    wx.showToast({
      title: `点击了图表: ${item.title}`,
      icon: 'none'
    });
    // TODO: 跳转到图表详情页
  },

  /**
   * 图表组件点击事件
   */
  onChartClick(e) {
    const {item} = e.detail;
    console.log('点击图表:', item);

    if (!this.data.isLogin) {
      this.redirectToLogin();
      return;
    }

    wx.showModal({
      title: '查看图表',
      content: `即将查看图表：${item.title}`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 处理智库点击
   */
  handleLibraryClick(item) {
    wx.showToast({
      title: `点击了智库: ${item.title}`,
      icon: 'none'
    });
    // TODO: 跳转到智库详情页
  },

  /**
   * 页面点击事件 - 用于关闭下拉框
   */
  onPageTap(e) {
    // 如果点击的不是排序相关元素，关闭下拉框
    const {showSortDropdown, showChartSortDropdown} = this.data;
    if (showSortDropdown || showChartSortDropdown) {
      this.setData({
        showSortDropdown: false,
        showChartSortDropdown: false
      });
    }
  },

  /**
   * 点击排序下拉框
   */
  onSortDropdownTap() {
    this.setData({
      showSortDropdown: !this.data.showSortDropdown
    });
  },

  /**
   * 排序选项容器点击事件 - 阻止冒泡但不关闭下拉框
   */
  onSortOptionsContainerTap() {
    // 使用 catchtap 已经阻止了事件冒泡，这里不需要额外处理
  },

  /**
   * 选择排序选项
   */
  onSortOptionTap(e) {
    const {value, text} = e.currentTarget.dataset;
    const {currentSort} = this.data;

    // 如果选择的是当前排序，直接关闭下拉框
    if (value === currentSort) {
      this.setData({showSortDropdown: false});
      return;
    }

    // 更新排序状态和请求参数
    this.setData({
      currentSort: value,
      currentSortText: text,
      showSortDropdown: false,
      libraryRequestParams: {
        ...this.data.libraryRequestParams,
        sort: value
      }
    });
  },

  /**
   * 点击图表排序下拉框
   */
  onChartSortDropdownTap() {
    this.setData({
      showChartSortDropdown: !this.data.showChartSortDropdown,
      // 关闭其他下拉框
      showSortDropdown: false
    });
  },

  /**
   * 图表排序选项容器点击事件 - 阻止冒泡但不关闭下拉框
   */
  onChartSortOptionsContainerTap() {
    // 使用 catchtap 已经阻止了事件冒泡，这里不需要额外处理
  },

  /**
   * 选择图表排序选项
   */
  onChartSortOptionTap(e) {
    const {value, text} = e.currentTarget.dataset;
    const {currentChartSort} = this.data;

    // 如果选择的是当前排序，直接关闭下拉框
    if (value === currentChartSort) {
      this.setData({showChartSortDropdown: false});
      return;
    }

    // 更新排序状态
    this.setData({
      currentChartSort: value,
      currentChartSortText: text,
      showChartSortDropdown: false
    });

    // 根据排序方式对图表数据进行排序
    this.sortChartList(value);
  },

  /**
   * 对图表列表进行筛选
   */
  sortChartList(filterType) {
    // 获取原始图表数据（重新加载以确保数据完整）
    this.loadChartData();

    // 等待数据加载完成后再进行筛选
    setTimeout(() => {
      const allChartList = this.data.chartList;
      let filteredList = [...allChartList];

      // 根据筛选类型进行筛选
      if (filterType !== 'all') {
        filteredList = allChartList.filter(
          item => item.category === filterType
        );
      }

      this.setData({
        chartList: filteredList
      });
    }, 50);
  },

  /**
   * 撼地智库数据变化回调
   */
  onLibraryDataChange(e) {
    console.log('撼地智库数据变化:', e.detail);
    const {list = [], total = 0, hasMore = false, isEmpty = false} = e.detail;

    this.setData({
      libraryReportList: list,
      libraryTotal: total // 保存总数，用于显示
    });

    console.log('撼地智库数据更新:', {
      listLength: list.length,
      total: total,
      hasMore: hasMore,
      isEmpty: isEmpty
    });
  },

  /**
   * 撼地智库API请求错误处理
   */
  onLibraryError(e) {
    console.error('撼地智库API请求失败:', e.detail);
    // 错误处理已在组件内部完成
  },

  /**
   * 撼地智库研报点击事件
   */
  onLibraryReportClick(e) {
    const {item} = e.detail;
    console.log('点击撼地智库研报:', item);

    // 显示操作选择弹窗
    const itemList = ['在线预览', '下载到本地'];
    wx.showActionSheet({
      itemList,
      success: res => {
        switch (res.tapIndex) {
          case 0:
            this.previewReport(item);
            break;
          case 1:
            this.downloadReport(item);
            break;
        }
      },
      fail: () => {
        console.log('用户取消操作');
      }
    });
  },

  /**
   * 在线预览研报
   */
  previewReport(reportItem) {
    wx.showToast({
      title: `预览: ${reportItem.title}`,
      icon: 'none'
    });
    // TODO: 实现真实的预览功能
  },

  /**
   * 下载研报到本地
   */
  downloadReport(reportItem) {
    wx.showToast({
      title: `下载: ${reportItem.title}`,
      icon: 'none'
    });
    // TODO: 实现真实的下载功能
  },

  /**
   * 一次性计算所有高度
   */
  calculateAllHeights() {
    setTimeout(() => {
      if (this.data.report_name) {
        this.calculateHeightByTab(this.data.currentTab);
      } else {
        this.calculateGeneralHeight();
      }
    }, 100);
  },
  /**
   * 计算撼地智库的高度 - 参考thinkTankList的正确实现
   */
  calculateLibraryHeight() {
    try {
      // 延迟执行，确保DOM渲染完成
      setTimeout(() => {
        getHeight(this, ['.company_num'], data => {
          const {screeHeight, res} = data;

          // 获取company_num的top值（距离顶部的距离）
          const barH = res[0]?.top + res[0]?.height || 0;

          // 计算列表区域高度：页面总高度 - company_num距离顶部的高度
          const listScrollHeight = screeHeight - barH;

          this.setData({
            listScrollHeight: Math.max(listScrollHeight, 600),
            isHeightCalculated: true
          });
        });
      }, 300);
    } catch (error) {
      this.setData({
        listScrollHeight: 400,
        isHeightCalculated: true
      });
    }
  },

  /**
   * 计算报告tab的高度 - 报告tab现在也有LibraryReportList组件
   */
  calculateReportHeight() {
    try {
      // 延迟执行，确保DOM渲染完成
      setTimeout(() => {
        // 报告tab需要考虑专题区域和统计信息的高度
        getHeight(this, ['.zt-container', '.company_num'], data => {
          const {screeHeight, res} = data;

          // 获取专题容器的bottom值（专题区域结束位置）
          const ztContainerBottom = res[0] ? res[0].top + res[0].height : 200;

          // 获取统计信息的高度（如果存在且是VIP用户）
          const companyNumHeight =
            res[1] && this.data.isVip ? res[1].height : 0;

          // 计算列表区域高度：页面总高度 - 专题区域结束位置 - 统计信息高度 - 一些边距
          const listScrollHeight =
            screeHeight - ztContainerBottom - companyNumHeight - 20;

          this.setData({
            listScrollHeight: Math.max(listScrollHeight, 400),
            isHeightCalculated: true
          });
        });
      }, 300);
    } catch (error) {
      this.setData({
        listScrollHeight: 400,
        isHeightCalculated: true
      });
    }
  },

  /**
   * 计算图表tab的高度
   */
  calculateChartHeight() {
    try {
      // 延迟执行，确保DOM渲染完成
      setTimeout(() => {
        // 图表tab需要考虑统计信息的高度
        getHeight(this, ['.company_num'], data => {
          const {screeHeight, res} = data;

          // 获取统计信息的top值和高度
          const companyNumTop = res[0]?.top || 0;
          const companyNumHeight = res[0]?.height || 0;

          // 计算列表区域高度：页面总高度 - 统计信息结束位置 - 一些边距
          const listScrollHeight =
            screeHeight - companyNumTop - companyNumHeight - 20;

          this.setData({
            listScrollHeight: Math.max(listScrollHeight, 400),
            isHeightCalculated: true
          });
        });
      }, 300);
    } catch (error) {
      this.setData({
        listScrollHeight: 400,
        isHeightCalculated: true
      });
    }
  },

  /**
   * 计算通用高度
   */
  calculateGeneralHeight() {
    try {
      getHeight(this, ['.searchs'], data => {
        const {screeHeight, res} = data;

        // 安全获取搜索框高度
        const searchHeight =
          res && res[0] && res[0].height ? res[0].height : 80;

        // 计算历史记录区域高度
        const scrollHeight = screeHeight - searchHeight;

        this.setData({
          scrollHeight: Math.max(scrollHeight, 300),
          isHeightCalculated: true
        });
      });
    } catch (error) {
      this.setData({
        scrollHeight: 400,
        isHeightCalculated: true
      });
    }
  }
});
