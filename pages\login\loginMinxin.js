import constant from '../../utils/constant';
import {getCurRoute, format} from '../../utils/route';
import {user, mine} from '../../service/api';
import {commonRequest} from '../../utils/mixin/loginbeforeReq';
import {acceptPageLink} from '../../utils/mixin/pageShare';
const app = getApp();
const ary = [
  //登录后重定向的页面
  '/pages/home/<USER>',
  '/pages/login/login',
  '/pages/enterprise/index',
  '/pages/report/report'
];
const ary1 = [
  //登录后返回的页面
  '/articledetails/articledetails',
  '/searchs/searchs', // 查企业列表
  '/searTerm/sear-term',
  // "/webs/index", // 企业详情页面这个不行 需要手动触发
  '/searchPark/index',
  '/IndustryListMasonry/index', // 产业链企业详情
  '/science/scienceDetail/index', //科企查询详情
  '/authoritativeList/authoritativeList', //权威榜单
  '/authoritativeDetail/authoritativeDetail', //权威榜单详情
  '/searchPark/index', //查园区
  '/searchPark/detail/index' //查园区详情
  // '/mine/mineinvite/mineinvite'
];
const ary2 = ['/mine/mineinvite/mineinvite'];

const reg =
  /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/; // 校验手机号
// const reg = /^/; // 校验手机号
module.exports = function (obj) {
  return Behavior({
    properties: {
      gobackPath: {
        type: String,
        value: undefined
      },
      isCard: {
        type: Boolean,
        value: false
      }
    },
    data: {
      userInfo: {},
      code: '',
      canIUseGetUserPropfile: true
    },
    methods: {
      login() {
        const _this = this;
        wx.login({
          success(res) {
            if (res.code) {
              _this.setData(
                {
                  codes: res.code
                },
                () => {
                  // 每次code都要从新获取--注册不需要走登录接口--以前老代码 没有登录过就注册
                  // _this.data.canIUseGetUserPropfile && _this.goLogin()
                }
              );
            } else {
              wx.showToast({
                title: '登录失败',
                icon: 'error',
                duration: 2000
              });
              console.log('登录失败！' + res.errMsg);
            }
          }
        });
      },
      // 微信登录
      getUserNumber(res) {
        const {encryptedData, iv} = res.detail;
        const _this = this;
        if (encryptedData) {
          const reGparams = {
            we_chat_code: _this.data.codes,
            we_chat_name: '',
            code: _this.data.codes,
            iv: iv,
            encrypt_data: encryptedData,
            user_source: _this.data.user_source
          };
          app.showLoading('正在登录');
          // user.wxLogin
          user
            .register(reGparams)
            .then(ress => {
              if (ress?.new_registered) {
                wx.setStorageSync('new_registered', ress.new_registered);
              }
              wx.setStorageSync('TOKEN', ress);
              user.newGetUserInfo(ress.user_id).then(userData => {
                wx.setStorageSync(constant.UserInfo, JSON.stringify(userData));
                _this.goHome();
              });
            })
            .catch(e => {
              console.log('登录报错信息', e);
              wx.hideLoading();
              app.showToast(e.data?.message || '登录失败!', 'none', 1200);
            });
        }
      },

      // 一些缓存--同步
      async getCommonReq() {},
      // 登录成功之后获取权限列表
      async privilege() {
        const res = await user.privileges();
        wx.setStorageSync('privilege', res);
      },
      // 登录成功之后，获取用户个人中心的信息 --获取登录分享码
      async getMyData() {
        const res = await mine.getMyData();
        wx.setStorageSync('userData', res);
        // 获取分享链接
        const res1 = await mine.generatePageLink();
        wx.setStorageSync('generatePageLink', res1.serial_number);
        // 掉别人分享的连接
        acceptPageLink();
      },
      // 登录成功之后--获取用户的openId
      async getopenId() {
        const res = await user.openId();
        if (res) {
          wx.setStorageSync('openId', res?.member_info?.we_chat_id || '');
        }
      },

      // 进入对应页面
      async goHome() {
        const that = this;
        let url = this.data.gobackPath || '/pages/home/<USER>';
        // 走到这步说明 登录已经完成了
        app.isLogin();
        await this.privilege();
        await this.getMyData();
        this.getopenId();
        commonRequest();
        this.getCommonReq();
        // setTimeout(() => {
        // }, 100)
        if (this.data.isCard) {
          let pages = getCurrentPages();
          if (pages.length > 0) {
            let len = pages.length - 1;
            pages[len].onLoad();
            pages[len].onShow();
            // 处理check.js混入
            pages[len]?.handleLoginStatus && pages[len]?.handleLoginStatus();
          }
          return;
        }
        // 路由 ---这里有bug需要处理  （如果带有id那种，id会丢失 也不能直接写死reLaunch，要讲路由移除一个出去）
        let preUrl = `${
          getCurRoute(2)?.slice(0, 1) == '/'
            ? getCurRoute(2)
            : '/' + getCurRoute(2)
        }`; //进入登录页面的路由
        if (!preUrl || preUrl == '/undefined') {
          preUrl = '/pages/home/<USER>';
        }
        console.log('登录后跳转的路由url:' + url, 'preUrl:' + preUrl);
        if (wx.getStorageSync('tokenInvalid')) {
          //token失效的情况，后台改了套餐
          console.log('tokenInvalid失效的情况 ');
          this.setData({
            istokenInvalid: undefined
          });
          wx.removeStorageSync('tokenInvalid');
          app.route(this, '/pages/home/<USER>', 'reLaunch');
          return;
        }
        const urlStr = ary1.includes(format(url).split('?')[0]); // 去掉路径中多余的参数，防止影响判断
        // 如果是卡片页面点击登录的，就不跳转-触发生命周期刷新页面
        if (ary.includes(url)) {
          //登录后到tab页面
          console.log('ary跳转', url);
          app.route(this, url, 'reLaunch');
        } else if (urlStr) {
          //ary1是登录后也回到当前页面
          // 如果有上级路由就返回没有就重定向到这个路由(因为有分享的问题)
          console.log('urlStr跳转 navigateBack', urlStr);
          app.route(this, 1, 'navigateBack');
        } else if (ary2.includes(format(url))) {
          if (this.data.paramsCode) {
            url += `?${this.data.paramsCode}`;
          }
          // console.log('登录后重定向的路由', url)
          app.route(this, url, 'redirectTo');
        } else {
          console.log('preUrl跳转 reLaunch', urlStr);
          wx.reLaunch({
            url: preUrl,
            success: function (res) {
              //
              setTimeout(() => {
                app.route(that, url, 'navigateTo');
              }, 100);
            }
          });
        }
      }
    },
    pageLifetimes: {
      show() {
        this.setData({
          canIUseGetUserPropfile: true
        });
        // console.log(this.data)
      }
    }
  });
};
