import {project, user} from '../../../service/api';
import {getOrgData} from '../../../service/user';
import {hasPrivile} from '../../../utils/route';
var checkMixin = require('../../../utils/mixin/check');
const app = getApp();
Page({
  behaviors: [checkMixin()],
  data: {
    loginFlag: false,
    visible: false,
    islogin: app.globalData.login,
    selecData: [
      {
        code: 'personalview',
        key: 'personal',
        name: '个人视图'
      },
      {
        code: 'personal',
        key: 'personal',
        name: '个人'
      }
    ],
    selectList: {
      list: [],
      label: '团队'
    },
    selectViewData: [
      {
        code: 'manageview',
        key: 'group',
        name: '管理视图'
      },
      {
        code: 'personalview',
        key: 'personal',
        name: '个人视图'
      }
    ],
    selectRangeData: [
      {
        code: 'manage',
        key: 'group',
        name: '全团队'
      },
      {
        code: 'personal',
        key: 'personal',
        name: '个人'
      }
    ],
    manageList: [
      {
        name: '成员管理',
        key: 'clue_count',
        count: '',
        total: '',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/call.png',
        url: '/childSubpackage/pages/memberManagement/index'
      },
      {
        name: '项目分配',
        key: 'valid_count',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/project.png',
        url: '/childSubpackage/pages/projectManagement/index'
      }
    ],
    sortList: [],
    boardList: [
      {
        name: '全部项目',
        key: 'clue_count',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/all.png',
        number: ''
      },
      {
        name: '未跟进',
        label: '未跟进',
        key: 'valid_count',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/prefollow.png',
        number: '',
        color: '-webkit-linear-gradient(top, #FD9331, #FFE1BD)'
      },
      {
        name: '在谈项目',
        label: '在谈',
        key: 'visit_count',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/following.png',
        number: '',
        color: '-webkit-linear-gradient(top, #5DD8FF, #4AB8FF)'
      },
      {
        name: '有效项目',
        label: '有效',
        key: 'subscribe_count',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/valid.png',
        number: '',
        color: '-webkit-linear-gradient(top, #2BE4D5 ,#06BDC1)'
      },
      {
        name: '到访项目',
        label: '到访',
        key: 'contract_count',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/visit.png',
        number: '',
        color: '-webkit-linear-gradient(top, #1E75DB, #95BCEB)'
      },
      {
        name: '签约项目',
        label: '签约',
        key: 'clinch_count',
        icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/business/sign.png',
        number: '',
        color: '-webkit-linear-gradient(top, #9C85DB, #D3C8F3)'
      }
    ]
  },

  sortFun() {
    let temps = this.data.boardList.slice(1, 6).map(item => {
      return {
        number: item.number,
        color: item.color,
        name: item.label
      };
    });
    for (var i = 0; i < temps.length - 1; i++) {
      // 内层循环,控制比较的次数，并且判断两个数的大小
      for (var j = 0; j < temps.length - 1 - i; j++) {
        // 白话解释：如果前面的数大，放到后面(当然是从小到大的冒泡排序)
        if (temps[j].number < temps[j + 1].number) {
          var temp = temps[j];
          temps[j] = temps[j + 1];
          temps[j + 1] = temp;
        }
      }
    }
    this.setData({
      sortList: temps
    });
  },

  async getBusinessData() {
    const {org_id, person_vip_type, self_flag} = wx.getStorageSync('userData');
    this.setData({
      isAdmin: person_vip_type == 2 && self_flag
    });

    let temp =
      this.data.selecData[0].code == 'personalview'
        ? 'personal'
        : this.data.selecData[1].key;

    Promise.all([project.getBusinessRef(temp)]).then(res => {
      // let res={total:7128,unassigned:1534,assigned:765,valid:357,visited:238,signed:4234}
      this.setData({
        'boardList[0].number': res[0].total != undefined ? res[0].total : '',
        'boardList[1].number':
          res[0].unassigned != undefined ? res[0].unassigned : '',
        'boardList[2].number':
          res[0].talking != undefined ? res[0].talking : '',
        'boardList[3].number': res[0].valid != undefined ? res[0].valid : '',
        'boardList[4].number':
          res[0].visited != undefined ? res[0].visited : '',
        'boardList[5].number': res[0].signed != undefined ? res[0].signed : ''
      });
      console.log(res);
      if (res[0].total != undefined) {
        this.sortFun();
      }
      if (this.data.isAdmin) {
        getOrgData(org_id).then(res => {
          this.setData({
            'manageList[0].count': res.current_user_num,
            'manageList[0].total': res.user_limit
          });
          wx.hideLoading();
        });
      } else {
        wx.hideLoading();
      }
    });
  },
  onLoad() {
    let data = app.isLogin();
    if (data) {
      this.setData({
        loginFlag: true
      });
    }
  },
  onShow() {},
  handleLogin() {
    //已经登录触发的函数
    this.setData({
      loginFlag: true
    });
    app.showLoading('加载中');
    this.getBusinessData();
    this.getTeamList();
    const {
      org_data: {org_name, org_id},
      package_data: {package_type}
    } = wx.getStorageSync('userData');
    this.setData({
      org_name:
        org_name.length > 5
          ? org_name.slice(0, 5) + '...的团队'
          : org_name + '的团队',
      org_id: org_id,
      isShowTeam: package_type == '团队VIP'
    });
  },
  handleLogout() {
    this.setData({
      loginFlag: false
    });
  },
  //选择事件
  selectValue(e) {
    switch (this.data.selectList.label) {
      case '视图':
        this.setData({
          'selecData[0]': e.currentTarget.dataset.item
        });
        break;
      case '项目范围':
        this.setData({
          'selecData[1]': e.currentTarget.dataset.item
        });
        break;
    }

    this.setData({
      visible: false
    });
    this.getBusinessData();
  },
  // 弹窗事件
  select(e) {
    this.setData({
      teamPop: true
    });
  },
  onTeamPop(e) {
    if (!e.detail) {
      this.setData({
        teamPop: false
      });
      return;
    }
    // 由内容的处理
    let {org_name, org_id} = e.detail;
    this.setData({
      org_name:
        org_name.length > 5
          ? org_name.slice(0, 5) + '...的团队'
          : org_name + '的团队',
      org_id: org_id,
      teamPop: false
    });
  },
  jumpProjectDetail(e) {
    let arr = '';
    let temp = ['未跟进', '有效', '在谈', '到访', '签约'];
    temp.map((item, index) => {
      if (e.currentTarget.dataset.item.label == item) {
        arr = index;
      }
    });
    let url =
      e.currentTarget.dataset.item == 0
        ? `../../companyPackage/pages/projectManage/index`
        : `../../companyPackage/pages/projectManage/index?project_process=${arr}`;
    app.route(this, url);
  },
  goPage({currentTarget: {dataset}}) {
    const {url} = dataset.item;
    app.route(this, url);
  },
  async getTeamList() {
    try {
      let {user_id} = JSON.parse(wx.getStorageSync('user-info'));
      const [err, res] = await app.to(user.userTeam(user_id));
      if (res.length <= 1) {
        this.setData({
          teamList: []
        });
        return;
      }
      let arr = res.map(i => {
        return {
          ...i.organization,
          user_id: i.user_id
        };
      });
      let reqAry = arr.map(i => {
        return {
          org_id: i.org_id,
          user_id: i.user_id
        };
      });
      const [err1, res1] = await app.to(user.statisticsMessageCount(reqAry));
      arr = arr.map(item => {
        res1.forEach(i => {
          if (item.org_id == i.org_id) {
            item['message_count'] = i.message_count;
          }
        });
        return item;
      });
      this.setData({
        teamList: arr
      });
    } catch (err) {
      console.log(err);
    }
  }
});
