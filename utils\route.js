import {dataPrivilegeControl} from '../service/pay';
// /companyPackage/pages /searchs/searchs  ?id=11"取/searchs/searchs
const white = [
  '/login/login',
  '/errPage/errPage',
  '/searchs/searchs',
  '/articledetails/articledetails',
  '/searchBusiness/searchBusiness',
  '/industryChain/chainListNew/chainList', // 查业务企业列表
  '/industryChain/chainSearch/chainSearch', // 产业链搜索页面
  // 首页那几个
  '/science/scienceEnter/index',
  '/financing/home/<USER>',
  '/mineRelation/relation',
  '/merchantsMap/merchants',
  '/searTerm/sear-term',
  '/authoritativeList/authoritativeList',
  '/searchPark/index',
  '/companyInfo/companyInfo',
  '/huntList/searchs',
  '/mineRelation/search/index',
  '/science/scienceDetail/index',
  '/login/agreement/index',
  '/mine/mineinvite/mineinvite',
  '/authoritativeDetail/authoritativeDetail',
  '/informationDetail/index'
];
const HomeRoute = [
  '/companyPackage/pages/business/business',
  '/report/report',
  '/home/<USER>',
  '/mine/mine'
];
const whiteRouteBtn = [
  //白名单
  'home',
  'searterm',
  'huntSear',
  'mineRelation'
];

function assert(reg, msg) {
  if (!reg) {
    console.warn(msg);
    // throw new Error(msg);
  }
}

function toQueryString(url, obj) {
  // 参数从对象转为字符串
  var ret = [];
  for (var key in obj) {
    key = encodeURIComponent(key);
    var values = obj[key];
    if (values && values.constructor == Array) {
      //数组
      var queryValues = [];
      for (var i = 0, len = values.length, value; i < len; i++) {
        value = values[i];
        queryValues.push(toQueryPair(key, value));
      }
      ret = ret.concat(queryValues);
    } else {
      //字符串
      ret.push(toQueryPair(key, values));
    }
  }
  return url + '?' + ret.join('&');
}

function getCurRoute(idx = 1) {
  // 获取当前路由
  let pages = getCurrentPages();
  let currPage = null;
  if (pages.length) {
    currPage = pages[pages.length - idx]; //1->當前路由 2为上级路由
  }
  // console.log(currPage)
  return currPage?.route || undefined;
}

function isLogin() {
  let token = wx.getStorageSync('TOKEN') || false;
  return token;
}

function format(url) {
  //截取支付串
  let endStr = url?.split('pages')[1];
  endStr = endStr?.split('*')[0]; //?
  //
  return endStr;
}

function restoreUrl(url) {
  let urls = url.replace(/\*/g, '?');
  urls = urls.replace(/\@/g, '=');
  //
  return urls;
}

// 路由拦截
function route(...args) {
  let curRoute = getCurRoute(),
    islogin = isLogin(),
    that = typeof args[0] && args.shift(); //第一个是this,必传 因为·里面会操作外面 that.setData({})，也可通过that拿到外面的上级路由
  let url = typeof args[0] && (args.shift() || 1); //第一个是url
  let type = args[args.length - 1] ? args.pop() : 'navigateTo'; //最后一个是类型
  // 路由拦截 跳转处理参数--可以单独拿一个方法出来处理
  let optionalA = args.length > 0 ? args.shift() : null; //第二个参数，不是最后一个
  // 判断是否登录-特殊处理--登录
  if (!islogin && url != 1) {
    url = url.replace(/\?/g, '*');
    url = url.replace(/\=/g, '@');
    url = white.includes(format(url))
      ? restoreUrl(url)
      : `/pages/login/login?url=${url.slice(0, 1) == '/' ? url : '/' + url}`;
    type = type;
  }
  if (url == '/pages/login/login') {
    url = `/pages/login/login?url=/${curRoute}`;
  }
  if (typeof url != 'number' && HomeRoute.includes(format(url))) {
    if (url == '/pages/mine/mine') {
      // 22.11.04新版直接去首页
      url = '/pages/home/<USER>';
    }
    type = 'switchTab';
  }
  // 特殊处理
  if (url == '/gohome') {
    url = '/pages/home/<USER>';
    type = 'switchTab';
  }
  //
  switch (type) {
    case 'navigateTo':
      assert(url && url != 1, '请传入url');
      wx.navigateTo({
        url
      }); //目前里面的回调还没有到，所以没做
      break;
    case 'reLaunch':
      assert(url && url != 1, '请传入url');
      wx.reLaunch({
        url
      });
      break;
    case 'redirectTo':
      assert(url && url != 1, '请传入url');
      wx.redirectTo({
        url
      });
      break;
    case 'navigateBack': //返回目前没有做拦截
      wx.navigateBack({
        delta: url
      });
      break;
    case 'switchTab': //返回目前没有做拦截
      wx.switchTab({
        url
      });
      break;
    default:
      break;
  }
}

// 是否有vip权限码
const privileData = {
  '首页-查企业-搜索': 'home-queryCompany-search',
  '首页-查企业-列表': 'home-queryCompany-list',
  '首页-查业务-列表': 'home-queryBusiness-search',
  '首页-查业务-关键词': 'home-queryBusiness-keywd',
  '企业猎搜-筛选条件页': 'companySearch-search',
  '企业猎搜-企业列表': 'companySearch-list',
  '产业链-首页': 'industrialChain-home',
  '产业链-搜索': 'industrialChain-search',
  '产业链-企业列表': 'industrialChain-list',
  '融资事件-列表': 'financeEvents-list',
  '地图招商-列表': 'mapBusiness-list',
  '找关系-基础关系': 'relationship-base',
  '找关系-查询结果': 'relationship-ret',
  '找关系-更多关系': 'relationship-more',
  '找关系-关系导出': 'relationship-export',
  '权威榜单-榜单列表': 'authRank-list',
  '权威榜单-搜索': 'authRank-search',
  '权威榜单-榜单详情': 'authRank-detail',
  '科技型企业-首页': 'techEnterprise-home',
  '科技型企业-列表页': 'techEnterprise-home',
  '风险监控-添加监控': 'monitor-add',
  '风险监控-监控列表': 'monitor-list',
  '风险监控-监控日报': 'monitor-daily',
  '查老板-老板列表': 'queryBoss-list',
  '查老板-搜索': 'queryBoss-search',
  '查老板-老板详情': 'queryBoss-detail',
  '查园区-园区列表': 'queryPark-list',
  '查园区-园区详情': 'queryPark-detail',
  '查图谱-首页': 'checkAtlas-home',
  '查图谱-搜索': 'checkAtlas-search',
  '查图谱-列表': 'checkAtlas-list',
  '查图谱-详情-企业图谱': 'checkAtlas-detail',
  '区域经济报告-首页': 'areaEconomyReport-home',
  '区域经济报告-搜索': 'areaEconomyReport-search',
  '区域经济报告-列表': 'areaEconomyReport-list',
  '区域经济报告-详情': 'areaEconomyReport-detail',
  '区域经济报告-对比': 'areaEconomyReport-compare',
  '产业研报-首页': 'estateReport-home',
  '产业研报-搜索': 'estateReport-search',
  '产业研报-列表': 'estateReport-list',
  '产业研报-详情': 'estateReport-detail',
  '失信被执行人-首页': 'unPromiseExecutedPerson-home',
  '失信被执行人-列表': 'unPromiseExecutedPerson-list',
  '失信被执行人-详情': 'unPromiseExecutedPerson-detail',
  被执行人: 'executedPerson',
  裁判文书: 'judicialDocuments',
  立案信息: 'filingInfo',
  终本案件: 'finalCase',
  开庭公告: 'earingAnnouncement',
  法院公告: 'courtAnnouncement',
  限制高消费: 'limitHighConsum',
  行政处罚: 'administrativeSanction',
  进出口信用: 'importAndExportCredit',
  '上市公司（主板）': 'mainBoardListing',
  '上市公司（港股）': 'hongKongShares',
  新三板: 'newThirdBoard',
  科创版: 'scientificAndCreativeEdition',
  查商标: 'checkTrademark',
  查专利: 'queryPatent',
  查著作权: 'queryCopyright',
  查网站: 'queryWebsite',
  项目管理: 'pm',
  发现: 'find',
  企业电话: 'companyPhone',
  企业详情报告: 'companyDetailReport'
};
const USER_TYPE_ENUM = {
  游客: 'rN=uiGHXKx',
  普通VIP: 'GXA=V1Q6!v',
  个人VIP: '%$dIV!6@jo',
  团队VIP: 'ixsD%sct87'
};
/*5cd4f1fa4032a1f3e8086b68e8c43d4c
privileType 风险监控-添加监控 传这个字段字段返回布尔决定展不展示,这里需要单独处理一下 ；
 packageType传这个拿到是属于普通用户，个人vip用户，还是团队vip用户 ,根据这个做对应渲染
*/
async function hasPrivile(obj) {
  let {privileType = '首页-查企业-搜索', packageType = false} = obj;
  let res1 = wx.getStorageSync('userData');
  let package_type = res1?.package_data?.package_type || '游客';
  if (packageType) {
    //判断是属于  普通VIP , 个人VIP ,团队VIP"
    return package_type;
  }
  // 改
  if (package_type == '个人VIP' || package_type == '团队VIP') {
    return true;
  }
  return false;
  // const privilege_code = privileData[privileType]
  // 去掉接口判断是否允许调用
  // const res = await dataPrivilegeControl({
  //   "privilege_code": privilege_code,
  //   "package_type": utilMd5.hexMD5(USER_TYPE_ENUM[package_type])
  // })
  // return res['enabled']
}
/*
ceshi: hijack(function (a) {}),//使用
    fn //需要传的参数
    点击事件拦截--作用没有登录，点击按钮让其去登录
*/
function hijack(fn, ...args) {
  // args格式{type:'xx',app:app} 后面还可传更多 isPop传了外面拿到一个布尔值通过this.data[type],控制跳转
  // 这里只在页面初始化的时候执行一次
  return function (event) {
    //这里可以往外传参数
    var context = this;
    let {type = '', app} = args[0] || {};
    //
    // 按钮--点击拦截--跳转登录
    if (!app.globalData.login && whiteRouteBtn.includes(type)) {
      // 针对个别的特殊处理
      if (args[0]?.isPop) {
        //
        app[type] = true; //增加一个布尔值，用来外面控制弹窗
        return;
      }
      app.route(context, '/pages/login/login');
      return;
    }
    // 按钮--点击拦截--vip
    fn.call(context, event);
  };
}

// 针对Ios支付的情况
function handlePayDev(that, app, type) {
  // let isIos = app.globalData.isIosPhone, text = 'ios系统暂不支持线上支付，请联系客服充值：19923989873';
  // if (isIos) {
  //     wx.showModal({
  //         title: '温馨提示',
  //         content: text,
  //         showCancel: false,
  //         confirmColor: '#20263A'
  //     })
  //     return
  // } else {
  //     app.route(that, '/subPackage/pages/payDetail/paydetail')
  // }
}

module.exports = {
  USER_TYPE_ENUM,
  route,
  toQueryString,
  hijack,
  getCurRoute,
  format,
  hasPrivile,
  handlePayDev
};

// type:wx.switchTab,wx.reLaunch,wx.redirectTo,wx.navigateTo,wx.navigateBack
// 使用或不适用可选参数调用实例函数
//    example(null, 'AA');
// 页面左上角返回按钮 有对应的监听周期
