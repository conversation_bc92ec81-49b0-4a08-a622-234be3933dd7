<CustomNavbar
  text="项目管理"
  id="navigationBar"
  class="navigationBar"
  textPositon="center"
  navColor="{{['#D2170D']}}"
  showNav="{{false}}"
  showBg="{{false}}"
/>
<view hidden="{{!loginFlag}}" class="business">
  <scroll-view
    style="height:90vh"
    scroll-y
    scroll-with-animation
    throttle="{{false}}"
  >
    <!-- 项目看板 -->
    <view class="board">
      <view class="board-title gradient">
        <text class="board-title-name" style="color: #fff;">项目看板</text>
        <view class="board-title-select">
          <view
            class="select-items"
            bindtap="select"
            data-item="selectViewData"
            wx:if="{{isShowTeam && login&&teamList.length>1 }}"
          >
            <text>{{org_name}}</text>
            <view class="select-icon">
              <image
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/white-array.png"
                mode="aspectFill"
              />
            </view>
          </view>
          <!-- <view class="select-items" wx:if="{{selecData[0].code=='manageview'}}" bindtap="select" data-item="selectRangeData">
            <text>{{selecData[1].name}}</text>
            <view class="select-icon">
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/cyt_v2/image/white-array.png" mode="aspectFill" />
            </view>
          </view> -->
        </view>
      </view>
      <view class="board-content">
        <view
          class="board-item"
          wx:for="{{boardList}}"
          wx:key="index"
          bindtap="jumpProjectDetail"
          data-item="{{item}}"
        >
          <view class="board-item-left">
            <image
              class="board-item-icon"
              src="{{item.icon}}"
              mode="aspectFit"
            />
          </view>
          <view class="board-item-right">
            <view class="board-item-name">{{item.name}}</view>
            <view class="board-item-number">{{item.number}}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="board" wx:if="{{isAdmin}}">
      <view class="board-title">
        <text class="board-title-name">团队管理</text>
      </view>
      <view class="board-content">
        <view
          class="board-item team"
          wx:for="{{manageList}}"
          wx:key="index"
          data-item="{{ item }}"
          bindtap="goPage"
        >
          <view class="board-item-right">
            <view class="board-item-name">{{item.name}}</view>
            <view class="board-item-number" wx:if="{{item.total}}"
              >{{item.count+'/'+item.total}}人</view
            >
          </view>
          <view class="board-item-left">
            <image
              class="board-item-icon"
              src="{{item.icon}}"
              mode="aspectFit"
            />
          </view>
        </view>
      </view>
    </view>
    <view class="board">
      <view class="board-title">
        <text class="board-title-name">项目漏斗</text>
      </view>
      <view class="board-content">
        <view class="indicator-show">
          <view class="grid-content" wx:for="{{sortList}}" :key="index">
            <view
              class="funnelChart"
              style="width:{{boardList[0].number==0?200:200+500*(item.number/boardList[0].number)}}rpx;height:104rpx;background-image: {{item.color}};"
            >
              <view
                class="left-triangle"
                style="border-right:{{index==4?50:250*(item.number-sortList[index+1].number)/boardList[0].number}}rpx solid transparent;border-bottom: 104rpx solid #fff;"
              >
              </view>
              <view class="board-content-text">
                <view>
                  {{item.name}}
                </view>
                <view style="font-weight: 600;">
                  {{item.number}}
                </view>
              </view>
              <view
                class="right-triangle"
                style="border-left:{{index==4?50:250*(item.number-sortList[index+1].number)/boardList[0].number}}rpx solid transparent;border-bottom: 104rpx solid #fff;"
              >
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <half-screen-pop
      zIndex="9999"
      title="{{'选择'+selectList.label}}"
      position="bottom"
      visible="{{visible}}"
      showFooter="{{false}}"
      confirmBtnText="取消"
      confirmBtnText="保存"
      footHeigh="168rpx"
      bindsubmit="submit"
      bindclose="close"
    >
      <view slot="customContent" class="custom_content">
        <view class="select-item-box">
          <view
            wx:for="{{selectList.list}}"
            wx:key="index"
            wx:for-item="item"
            class="select-item {{(selecData[0].code===item.code || selecData[1].code===item.code)?'active':''}}"
            data-item="{{item}}"
            catchtap="selectValue"
          >
            <text>{{item.name}}</text>
            <text class="select"></text>
          </view>
        </view>
      </view>
    </half-screen-pop>
  </scroll-view>
</view>
<!-- 团队切换 -->
<TeamPop
  title="团队切换"
  visible="{{teamPop}}"
  bindsubmit="onTeamPop"
  initVal="{{org_id}}"
  list="{{teamList}}"
/>
<!-- 未登录按钮 -->
<!-- <view class="noLbtn" wx:if="{{!loginFlag}}">
    <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>" class="img"></image>
    <view class="btn">
      <LoginBtn isCard text="登录"></LoginBtn>
    </view>
  </view> -->
<view class="card-login" hidden="{{loginFlag}}">
  <image
    src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/quesheng.png"
    mode="aspectFit"
  ></image>
  <view class="txt">未登录的用户暂时无法使用项目功能</view>
  <LoginBtn isCard></LoginBtn>
</view>
